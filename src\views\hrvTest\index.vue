<template>
	<div class="hrvInit">
		<div
			class="hrvInit-generateing"
			v-if="stage === RecordStage.REPORT_GENERATING"
		>
			<img
				src="@/assets/hrv/hrvfinished/hrvfinished_generate.gif"
				alt="gif"
			/>
			<span>
				{{ $t("hrv_generate_title_key") }}
			</span>
		</div>
		<template v-else>
			<div
				class="hrvInit-title"
				v-if="stage !== RecordStage.IMAGE_DISPLAY"
			>
				{{ $t("hrv_title_key") }}
			</div>
			<div class="hrvInit-content">
				<LoadingItems :stage="stage" :list="loadingItems" />
				<VideoImageBox :currentImage="currentImage" :stage="stage" />
				<VideoContent
					:retractCountdown="retractCountdown"
					:stage="stage"
					:errorPostureTips="errorPostureTips"
					:isShowErrorPostureTips="isShowErrorPostureTips"
					:totalTimeLeft="totalTimeLeft"
					ref="faceView"
					@faceStatus="handleFaceStatus"
				/>
				<ProcessLine
					:total="detectTotalTime"
					:stage="stage"
					:current="totalTimeLeft"
				/>
				<TipsBox
					:content="errorPostureTips"
					:visible="isShowErrorPostureTips"
				/>
				<div
					:class="
						isShowCountDown ? 'hrvInit-content-tips' : 'opacity-0'
					"
					v-if="stage !== RecordStage.IMAGE_DISPLAY"
				>
					<div class="hrvInit-content-tips-number">
						{{ countNumber }}
					</div>
					<div
						class="hrvInit-content-tips-text"
						:style="{
							fontSize: language.includes('en')
								? '1.7rem'
								: '2.25rem',
						}"
					>
						<p>{{ $t("hrv_test_tip1_key") }}</p>
						<p>{{ $t("hrv_test_tip2_key") }}</p>
						<p>{{ $t("hrv_test_tip3_key") }}</p>
					</div>
				</div>
			</div>
		</template>

		<div class="hrv_audio">
			<audio ref="hrvtestAudioRef" :src="hrvAudioMp3" autoplay></audio>
		</div>

		<div class="hrv_audio">
			<audio
				ref="hrvtestImageTipAudioRef"
				:src="hrvImageTipAudioMp3"
			></audio>
		</div>
	</div>
</template>
<script setup>
import { useHrvTest } from "./hooks"
import { onMounted, computed, onBeforeMount } from "vue"
import LoadingItems from "./components/LoadingItems.vue"
import VideoImageBox from "./components/VideoImageBox.vue"
import VideoContent from "./components/VideoContent.vue"
import ProcessLine from "./components/ProcessLine.vue"
import { RecordStage } from "../../constants/recordStage.js"
import { getCurrentLanguage } from "@/utils/i18n"
import TipsBox from "./components/TipsBox.vue"
const {
	detectTotalTime,
	stage,
	currentImage,
	totalTimeLeft,
	isShowErrorPostureTips,
	faceView,
	loadingItems,
	isShowCountDown,
	countNumber,
	hrvtestAudioRef,
	hrvAudioMp3,
	hrvtestImageTipAudioRef,
	hrvImageTipAudioMp3,
	errorPostureTips,
	retractCountdown,
  handleLinkEyeTracker,
	handleFaceStatus,
	initHrv,
} = useHrvTest()
const language = computed(() => getCurrentLanguage())
onMounted(() => {
  window.onGazeTrackingStarted=(status)=>{
    if(status){
      handleLinkEyeTracker()
      console.log('眼动追踪启动成功')
    }else{
      console.log('眼动追踪启动失败')
    }
  }
	initHrv()
})
</script>
<style lang="scss" scoped>
.hrvInit {
	width: 100vw;
	height: 100vh;
	background-size: 100% 100%;
	background: url("https://img3.airdoc.com/staticResources/data/static/js/hrvinit_back.png");
	font-weight: 500;
	font-size: 1.6rem;
	color: #333333;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	font-family: PingFang SC, PingFang SC;
	overflow: hidden;
	&-generateing {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		img {
			width: 25rem;
			margin-bottom: 8rem;
		}
		span {
			font-weight: 500;
			font-size: 2.25rem;
			color: #666666;
		}
	}
	&-title {
		height: 4.25rem;
		font-weight: 600;
		font-size: 3rem;
		color: #333333;
		line-height: 4.24rem;
		text-align: center;
		font-style: normal;
		text-transform: none;
		margin-bottom: 2rem;
	}
	&-content {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: flex-end;
		&-tips {
			min-width: 14rem;
			// height: 11.63rem;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			&-number {
				background: url("@/assets/hrv/hrvinit/hrvinit_number_back.png");
				background-size: 100% 100%;
				font-weight: 500;
				font-size: 3.25rem;
				color: #ffffff;
				width: 7.13rem;
				height: 7.13rem;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 1.5rem;
			}
			&-text {
				font-weight: 500;
				font-size: 2.25rem;
				color: #666666;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}
		}
		.opacity-0 {
			opacity: 0;
		}
	}
}
</style>
