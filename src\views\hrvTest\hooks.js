import { ref, watch, computed } from "vue"
import { useRouter, useRoute } from "vue-router"
import { useI18n } from "vue-i18n"
import { api } from "@/api"
import {
	goBackAppHomePage,
	notifyAndroidOnFinish,
	startEyeTracking,
	stopEyeTracking,
} from "@/utils/android.js"
import {
	MODAL_INIT,
	FACE_NOT_DETECTED,
	FACE_TOO_SMALL,
	FACE_TOO_LARGE,
	FACE_APPROPRIATE,
	FACE_TOO_CLOSE_TO_EDGE,
} from "../../constants/faceStatusConstants.js"
import { RecordStage } from "../../constants/recordStage.js"
import { useHrvStore } from "@/stores/hrvStore.js"
import { ElMessage } from "element-plus"
import { imageFilesNew } from "./file.js"

import hrvinit_monitor_png from "@/assets/hrv/hrvinit/hrvinit_monitor.png"
import hrvinit_loading_png from "@/assets/hrv/hrvinit/hrvinit_loading.png"
import hrvinit_test_load from "@/assets/hrv/hrvinit/hrvinit_test_load.png"
import hrvinit_network from "@/assets/hrv/hrvinit/hrvinit_network.png"
import hrvinit_ruler_png from "@/assets/hrv/hrvinit/hrvinit_ruler.png"
import { performSpeedTest } from "@/utils/api.js"

export function useHrvTest() {
	const hrvStore = useHrvStore()
	const router = useRouter()
	const route = useRoute()

	const { t } = useI18n()
	const state = {
		detectTotalTime: ref(90),
		centerDialogVisible: ref(false),
		faceStatus: ref(FACE_NOT_DETECTED),
		stage: ref(RecordStage.DETECT),
		isVideoSmall: ref(false),
		currentImage: ref({}),
		totalTimeLeft: ref(0),
		imageTimeLeft: ref(6),
		faceView: ref(null),
		timeDiffArray: computed(() => hrvStore.getTimeDiffArray),
		rgbMeanArray: computed(() => hrvStore.getRgbMeanArray),
		fps: ref(0),
		timer: ref(null),
		countDownTimer: ref(null),
		hrvtestAudioRef: ref(null),
		hrvAudioMp3: ref(""),
		hrvtestImageTipAudioRef: ref(null),
		hrvImageTipAudioMp3: ref(""),
		reportId: ref(""),
		userToken: ref(""),
		displayedImages: new Set(),
		hrvDetectMode: ref("scan"),
		loadModelFlag: computed(() => hrvStore.LoadModelFlag),
		// 眼动模型加载
		eyeModalLoadFlag: ref(false),
		deviceLoadFlag: ref(false),
		networkLoadFlag: ref(false),
		testSetLoadFlag: ref(false),
		distanceLoadFlag: ref(false),
		errorPostureTips: ref(""),
		isShowErrorPostureTips: ref(false),
		countNumber: ref(3),
		isShowCountDown: ref(false),
		retractCountdown: ref(false),
		loadedImagesList: ref([]),
		currentImageIndex: ref(0),
	}
	const loadingItems = computed(() => [
		// 设备自检
		{
			imageSrc: hrvinit_monitor_png,
			label: t("hrv_test_hook_device_load_key"),
			flag: deviceLoadFlag.value,
		},
		// 网络检测
		{
			imageSrc: hrvinit_network,
			label: t("hrv_test_hook_environment_load_key"),
			flag: networkLoadFlag.value,
		},
		// 模型加载
		{
			imageSrc: hrvinit_loading_png,
			label: t("hrv_test_hook_modal_load_key"),
			flag: eyeModalLoadFlag.value,
		},
		// 测试集加载
		{
			imageSrc: hrvinit_test_load,
			label: t("hrv_test_hook_testset_load_key"),
			flag: testSetLoadFlag.value,
		},
		// 距离检测
		{
			imageSrc: hrvinit_ruler_png,
			label: t("hrv_test_hook_distance_load_key"),
			flag: distanceLoadFlag.value,
		},
	])
	const {
		countDownTimer,
		detectTotalTime,
		centerDialogVisible,
		faceStatus,
		stage,
		isVideoSmall,
		currentImage,
		totalTimeLeft,
		imageTimeLeft,
		faceView,
		timeDiffArray,
		rgbMeanArray,
		fps,
		timer,
		hrvtestAudioRef,
		hrvAudioMp3,
		hrvtestImageTipAudioRef,
		hrvImageTipAudioMp3,
		reportId,
		userToken,
		displayedImages,
		hrvDetectMode,
		loadModelFlag,
		eyeModalLoadFlag,
		deviceLoadFlag,
		networkLoadFlag,
		testSetLoadFlag,
		distanceLoadFlag,
		errorPostureTips,
		isShowErrorPostureTips,
		retractCountdown,
		isShowCountDown,
		countNumber,
		loadedImagesList,
		currentImageIndex,
	} = state
	// 设备自检完成后，测网速
	watch(deviceLoadFlag, async (newValue) => {
		if (newValue) {
			const speedTestUrl =
				"https://ada-res.airdoc.com/resources/mpd/apk/speedtest.txt" // Example URL
			const expectedFileSize = 10 * 1024 * 1024 // 10MB, fallback if Content-Length is not available

			const speedKbps = await performSpeedTest(
				speedTestUrl,
				expectedFileSize
			)
			console.log("测速结果:", speedKbps, "kbps")

			if (speedKbps > 200) {
				// 200 kbps
				networkLoadFlag.value = true
				ElMessage.success(t("hrv_test_hook_network_good_key"))
			} else {
				networkLoadFlag.value = false
				ElMessage.error(t("hrv_test_hook_network_bad_key"))
        goBackAppHomePage()
			}
		}
	})
	watch(
		loadedImagesList,
		(newValue) => {
			// 成功大于5张图时，开始加载模型
			if (newValue.length >= 5 && loadModelFlag.value) {
				testSetLoadFlag.value = true
			}
		},
		{
			deep: true,
		}
	)
	// opencv模型加载，开启眼动
	watch(loadModelFlag, (newValue) => {
		console.log("🚀 ~ watch ~ newValue:", newValue)
		if (newValue) {
			startEyeTracking()
      setTimeout(()=>{
        eyeModalLoadFlag.value = true
      },3000)
		}
	})
	// 眼动服务加载完成后，获取预加载图片
	watch(eyeModalLoadFlag, (newValue) => {
		if (newValue) {
			getPreloadImagesList()
		}
	})

	const handleFaceStatus = (status) => {
		faceStatus.value = status
		const errorTipsMap = {
			[FACE_NOT_DETECTED]: () =>
				showError(t("face_not_detected_mix_key")),
			[FACE_TOO_SMALL]: () => showError(t("face_too_small_key")),
			[FACE_TOO_LARGE]: () => showError(t("face_too_large_key")),
			[FACE_TOO_CLOSE_TO_EDGE]: () =>
				showError(t("face_too_close_to_edge_key")),
			[FACE_APPROPRIATE]: () => startDetection(),
			[MODAL_INIT]: () => {},
		}
		errorTipsMap[status]?.()
	}

	const showError = (message) => {
		distanceLoadFlag.value = false
		isShowErrorPostureTips.value = true
		errorPostureTips.value = message
		if (countDownTimer) {
			clearInterval(countDownTimer.value)
			isShowCountDown.value = false
		}
	}

	const startDetection = () => {
		if (
			deviceLoadFlag.value &&
			loadModelFlag.value &&
			networkLoadFlag.value &&
			testSetLoadFlag.value &&
			stage.value !== RecordStage.IMAGE_DISPLAY
		) {
			distanceLoadFlag.value = true
			isShowErrorPostureTips.value = false
			isShowCountDown.value = true
			startCountDown()
		} else {
			isShowErrorPostureTips.value = false
			errorPostureTips.value = ""
		}
	}

	const startCountDown = () => {
		isShowCountDown.value = true
		countNumber.value = 3
		// 进入图片展示页面开启眼动追踪
		countDownTimer.value = setInterval(() => {
			countNumber.value--
			if (stage.value === RecordStage.IMAGE_DISPLAY) {
				clearInterval(countDownTimer.value)
				return
			}
			if (countNumber.value <= 0) {
				clearInterval(countDownTimer.value)
				isShowCountDown.value = false

				startTest()
			}
		}, 1000)
	}
	const preloadImages = (urls) => {
		urls.forEach((url) => {
			const img = new Image()
			img.src = url.materialPath
			url.points = []
			loadedImagesList.value.push(url)
		})
	}
	const getNextImage = () => {
		let nextImage = null
		if (displayedImages.size < loadedImagesList.value.length) {
			do {
				const imageIndex = Number(currentImageIndex.value)
				// 记录开始时间
				const startTimeStamp = Date.now()
				loadedImagesList.value[imageIndex].startTimeStamp =
					startTimeStamp
				nextImage = loadedImagesList.value[imageIndex]
			} while (displayedImages.has(nextImage))
			displayedImages.add(nextImage)
		}
		return nextImage
	}
	const startImageDisplay = async () => {
		hrvtestAudioRef.value.pause()
		hrvtestImageTipAudioRef.value.play()

		const displayNextImage = async () => {
			currentImage.value = getNextImage()
			imageTimeLeft.value = currentImage.value?.duration || 0
		}

		timer.value = setInterval(async () => {
			totalTimeLeft.value--
			imageTimeLeft.value--

			if (imageTimeLeft.value <= 0) {
				// 记录结束时间
				const endTimeStamp = Date.now()
				console.log(
					loadedImagesList.value[currentImageIndex.value],
					currentImageIndex.value,
					"firstImageIndex.value"
				)
				loadedImagesList.value[currentImageIndex.value].endTimestamp =
					endTimeStamp
				currentImageIndex.value++

				await displayNextImage()
			}
			if (totalTimeLeft.value <= detectTotalTime.value - 3) {
				retractCountdown.value = true
			}
			if (totalTimeLeft.value <= 0) {
				stopEyeTracking()
				stage.value = RecordStage.REPORT_GENERATING
				clearInterval(timer.value)
				console.log(loadedImagesList.value, "loadedImagesList.value")
				const gazePoints = loadedImagesList.value.map((item) => {
					return {
						startTimeStamp: item.startTimeStamp,
						endTimestamp: item.endTimestamp,
						points: item.points,
						materialId: item.materialId,
					}
				})
				await hrvStore.sendDataToBackend(router, route, gazePoints)
			}
			if (faceView.value?.faceView) {
				hrvStore.setTimeDiffArray(
					faceView.value.faceView.timeDiffArray.slice()
				)
				hrvStore.setRgbMeanArray(
					faceView.value.faceView.rgbMeanArray.slice()
				)
				fps.value = faceView.value.faceView.fps.toFixed(2)
			}
		}, 1000)

		await displayNextImage()
	}

	const startTest = () => {
		if (!userToken.value) {
			ElMessage.error(t("hrv_test_hook_without_token_key"))
			setTimeout(() => {
				handleExit()
			}, 2000)
			return
		}
		if (
			faceStatus.value === FACE_APPROPRIATE &&
			stage.value === RecordStage.DETECT
		) {
			stage.value = RecordStage.IMAGE_DISPLAY
			isVideoSmall.value = true
			startImageDisplay()
		}
	}

	const handleGoBack = () => {
		const result = notifyAndroidOnFinish()
		if (!result) {
			const params = new URLSearchParams(window.location.search)
			const urlParam = params.toString()
			router.replace("/hrvinit?" + urlParam).then(() => {
				window.location.reload()
			})
		}
	}

	const handleExit = () => {
		centerDialogVisible.value = false
		goBackAppHomePage()
	}

	const parseConfig = () => {
		const urlParams = new URLSearchParams(window.location.search)
		const config = urlParams.get("config")

		if (config) {
			try {
				const decodedConfig = decodeURIComponent(config)
				const configObj = JSON.parse(decodedConfig)

				if (configObj.duration) {
					totalTimeLeft.value = parseInt(configObj.duration, 10)
				}
			} catch (error) {
				console.error("Error parsing config:", error)
			}
		}
	}
	let ws = null

	const handleLinkEyeTracker = () => {
		ws = new WebSocket("ws://localhost:9200/Tracker")
		ws.onopen = function () {
			ws.send("hi")
			console.log("已连接")
			eyeModalLoadFlag.value = true
		}
		ws.onmessage = (event) => {
			handleEyeMoveData(event)
			// console.log(event.data, "眼动软件数据")
		}
		ws.onclose = () => {
			console.log("连接关闭")
			eyeModalLoadFlag.value = false
		}
		ws.onerror = () => {
			console.log("连接出错")
			eyeModalLoadFlag.value = false
		}
	}
	const handleEyeMoveData = (data) => {
		if (stage.value === RecordStage.IMAGE_DISPLAY) {
			const parseData = JSON.parse(data.data)
			const eyePoint = {
				x: parseData?.x,
				y: parseData?.y,
				dist: parseData?.dist,
			}
			if (
				currentImageIndex.value !== null &&
				loadedImagesList.value.length > 0
			) {
				loadedImagesList.value[currentImageIndex.value].points.push(
					eyePoint
				)
			}
		}
	}
	const getPreloadImagesList = async (retryCount = 0) => {
		try {
			const response = await api.get("/api/hrv/init")
			if (Number(response.data.code) === 0) {
				preloadImages(response.data.data.materialList)
				detectTotalTime.value = response.data.data.totalDuration
			} else {
				ElMessage.error(response.data.msg)
			}
		} catch (error) {
			if (retryCount < 2) {
				return await getPreloadImagesList(retryCount + 1)
			} else {
				ElMessage({
					type: "error",
					message: t("hrv_preload_error"),
					duration: 1500,
					onClose: () => {
						goBackAppHomePage()
					},
				})
				return false
			}
		}
	}

	const initHrv = () => {
		const userLanguage = navigator.language
		if (userLanguage == "en-US") {
			hrvAudioMp3.value =
				"https://ada-res.airdoc.com/resources/mpd/static/audio/hrvtest_audio_en_new1.mp3"
			hrvImageTipAudioMp3.value =
				"https://ada-res.airdoc.com/resources/mpd/static/audio/hrv_image_tip_audio_en_new1.mp3"
		} else {
			hrvAudioMp3.value =
				"https://ada-res.airdoc.com/resources/mpd/static/audio/hrvtest_audio_zh_new1.mp3"
			hrvImageTipAudioMp3.value =
				"https://ada-res.airdoc.com/resources/mpd/static/audio/hrv_image_tip_audio_zh_new1.mp3"
		}
		const urlParams = new URLSearchParams(window.location.search)
		const user_token = urlParams.get("accessToken")
		const duration = urlParams.get("duration")
		totalTimeLeft.value = duration
			? parseInt(duration, 10)
			: detectTotalTime.value

		if (user_token) {
			userToken.value = user_token
			window.localStorage.setItem("user_token", user_token)
		}
		deviceLoadFlag.value = true

		// if (import.meta.env.VITE_MODE === "development") {
		// 	setTimeout(() => {
		// 		networkLoadFlag.value = true
		// 		eyeModalLoadFlag.value = true
    //     testSetLoadFlag.value = true
		// 	}, 2000)
		// }
	}

	return {
		preloadImages,
		detectTotalTime,
		loadingItems,
		centerDialogVisible,
		faceStatus,
		stage,
		isVideoSmall,
		currentImage,
		totalTimeLeft,
		imageTimeLeft,
		faceView,
		timeDiffArray,
		rgbMeanArray,
		fps,
		hrvtestAudioRef,
		hrvAudioMp3,
		hrvtestImageTipAudioRef,
		hrvImageTipAudioMp3,
		reportId,
		userToken,
		hrvDetectMode,
		loadModelFlag,
		deviceLoadFlag,
		networkLoadFlag,
		distanceLoadFlag,
		testSetLoadFlag,
		errorPostureTips,
		isShowErrorPostureTips,
		retractCountdown,
		isShowCountDown,
		countNumber,
		handleLinkEyeTracker,
		handleFaceStatus,
		startTest,
		handleExit,
		handleGoBack,
		parseConfig,
		initHrv,
	}
}
